package com.cdkit.modules.cm.performance.budget;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.api.budget.IAnnualBudgetApi;
import com.cdkit.modules.cm.api.budget.dto.BudgetSubjectInfoDTO;
import com.cdkit.modules.cm.api.budget.dto.CostAnnualBudgetDTO;
import com.cdkit.modules.cm.api.budget.dto.CostAnnualBudgetDetailDTO;
import com.cdkit.modules.cm.api.budget.request.CostAnnualBudgetSaveRequest;
import com.cdkit.modules.cm.api.budget.dto.CostAnnualBudgetDetailImportDTO;
import com.cdkit.modules.cm.performance.budget.converter.CostAnnualBudgetDetailImportConverter;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;

import java.util.List;
import com.cdkit.modules.cm.application.budget.AnnualBudgetApplication;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;
import com.cdkitframework.poi.excel.ExcelImportUtil;
import com.cdkitframework.poi.excel.entity.ImportParams;

// Step2 导入相关
import com.cdkit.modules.cm.domain.budget.model.dto.CenterCostImportDTO;
import com.cdkit.modules.cm.performance.budget.converter.CenterCostImportConverter;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelReader;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.Date;

// 预算科目校验相关
import com.cdkit.modules.cm.domain.businessdata.repository.CostBudgetSubjectRepository;
import com.cdkit.modules.cm.domain.businessdata.model.entity.CostBudgetSubjectEntity;
import java.util.Set;
import java.util.HashSet;
import java.util.stream.Collectors;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

/**
 * 年度总预算控制器
 * <AUTHOR>
 * @date 2025-07-30
 */
@Tag(name = "年度总预算管理")
@RestController
@RequestMapping("/cm/costAnnualBudget")
@RequiredArgsConstructor
@Slf4j
public class CostAnnualBudgetController implements IAnnualBudgetApi {

    private final AnnualBudgetApplication annualBudgetApplication;
    private final CostAnnualBudgetDetailImportConverter importConverter;
    private final CostBudgetSubjectRepository costBudgetSubjectRepository;

    @Override
    public Result<IPage<CostAnnualBudgetDTO>> queryPageList(CostAnnualBudgetDTO queryVO, Integer pageNo, Integer pageSize) {
        log.info("开始分页查询年度总预算列表，页码: {}, 每页数量: {}", pageNo, pageSize);
        
        try {
            // 转换查询条件
            CostAnnualBudgetEntity queryEntity = CostAnnualBudgetConverter.toEntity(queryVO);
            
            // 分页查询
            PageRes<CostAnnualBudgetEntity> pageRes = annualBudgetApplication.queryPageList(queryEntity, pageNo, pageSize);
            
            // 使用 MyBatis Plus 的分页对象
            IPage<CostAnnualBudgetDTO> page = new Page<>(pageNo, pageSize);
            if (pageRes != null) {
                page.setCurrent(pageRes.getCurrent());
                page.setSize(pageRes.getSize());
                page.setTotal(pageRes.getTotal());
                page.setRecords(CostAnnualBudgetConverter.toDTOList(pageRes.getRecords()));
            }
            
            log.info("分页查询年度总预算列表成功，总记录数: {}", page.getTotal());
            return Result.OK(page);
            
        } catch (Exception e) {
            log.error("分页查询年度总预算列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<CostAnnualBudgetDetailDTO> queryById(String id) {
        log.info("开始根据ID查询年度总预算详情（包含明细数据），ID: {}", id);

        try {
            // 查询主表数据
            CostAnnualBudgetEntity entity = annualBudgetApplication.queryById(id);
            if (entity == null) {
                log.warn("年度总预算不存在，ID: {}", id);
                return Result.error("年度总预算不存在");
            }

            // 查询明细数据
            List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList =
                annualBudgetApplication.queryBudgetDetailsByBudgetId(id);

            // 转换为详情DTO
            CostAnnualBudgetDetailDTO detailDTO = CostAnnualBudgetDetailConverter.toDetailDTO(entity, budgetDetailList);

            log.info("根据ID查询年度总预算详情成功，ID: {}, 预算编号: {}, 明细数量: {}",
                    id, detailDTO.getBudgetCode(),
                    detailDTO.getBudgetDetailList() != null ? detailDTO.getBudgetDetailList().size() : 0);

            return Result.OK(detailDTO);

        } catch (Exception e) {
            log.error("根据ID查询年度总预算详情失败，ID: {}", id, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> edit(CostAnnualBudgetDTO costAnnualBudget) {
        log.info("开始编辑年度总预算，ID: {}, 预算名称: {}", 
                costAnnualBudget != null ? costAnnualBudget.getId() : "null",
                costAnnualBudget != null ? costAnnualBudget.getBudgetName() : "null");
        
        try {
            if (costAnnualBudget == null) {
                return Result.error("年度总预算数据不能为空");
            }
            
            CostAnnualBudgetEntity entity = CostAnnualBudgetConverter.toEntity(costAnnualBudget);
            String id = annualBudgetApplication.edit(entity);
            
            log.info("编辑年度总预算成功，ID: {}, 预算名称: {}", id, costAnnualBudget.getBudgetName());
            return Result.OK("编辑成功");
            
        } catch (Exception e) {
            log.error("编辑年度总预算失败，ID: {}, 预算名称: {}", 
                    costAnnualBudget != null ? costAnnualBudget.getId() : "null",
                    costAnnualBudget != null ? costAnnualBudget.getBudgetName() : "null", e);
            return Result.error("编辑失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> delete(String id) {
        log.info("开始根据ID删除年度总预算，ID: {}", id);
        
        try {
            annualBudgetApplication.delete(id);
            log.info("根据ID删除年度总预算成功，ID: {}", id);
            return Result.OK("删除成功");
            
        } catch (Exception e) {
            log.error("根据ID删除年度总预算失败，ID: {}", id, e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> deleteBatch(String ids) {
        log.info("开始批量删除年度总预算，IDs: {}", ids);

        try {
            annualBudgetApplication.deleteBatch(ids);
            log.info("批量删除年度总预算成功，IDs: {}", ids);
            return Result.OK("批量删除成功");

        } catch (Exception e) {
            log.error("批量删除年度总预算失败，IDs: {}", ids, e);
            return Result.error("批量删除失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> generateNextBudgetCode() {
        log.info("开始生成下一个预算编号（当前年份）");

        try {
            String nextBudgetCode = annualBudgetApplication.generateNextBudgetCode();
            log.info("生成下一个预算编号成功，预算编号: {}", nextBudgetCode);
            return Result.OK(nextBudgetCode);

        } catch (Exception e) {
            log.error("生成下一个预算编号失败", e);
            return Result.error("生成预算编号失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> saveStep1(CostAnnualBudgetSaveRequest request) {
        log.info("开始保存年度预算第一步，预算名称: {}", request != null ? request.getBudgetName() : "null");

        try {
            if (request == null) {
                return Result.error("年度预算数据不能为空");
            }

            // 转换主表数据
            CostAnnualBudgetEntity mainEntity = CostAnnualBudgetSaveConverter.toEntity(request);

            // 转换明细数据
            List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList =
                    CostAnnualBudgetSaveConverter.toBudgetDetailInfoList(request.getBudgetDetailList());

            // 调用应用服务保存
            String id = annualBudgetApplication.saveStep1(mainEntity, budgetDetailList);

            log.info("保存年度预算第一步成功，预算名称: {}, ID: {}", request.getBudgetName(), id);
            return Result.OK("保存成功",id);

        } catch (Exception e) {
            log.error("保存年度预算第一步失败，预算名称: {}", request != null ? request.getBudgetName() : "null", e);
            return Result.error("保存失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> editStep1(CostAnnualBudgetSaveRequest request) {
        log.info("开始编辑年度预算第一步，预算ID: {}, 预算名称: {}",
                request != null ? request.getId() : "null",
                request != null ? request.getBudgetName() : "null");

        try {
            if (request == null) {
                return Result.error("年度预算数据不能为空");
            }

            if (!StringUtils.hasText(request.getId())) {
                return Result.error("年度预算ID不能为空");
            }

            // 转换主表数据
            CostAnnualBudgetEntity mainEntity = CostAnnualBudgetSaveConverter.toEntity(request);

            // 转换明细数据
            List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList =
                    CostAnnualBudgetSaveConverter.toBudgetDetailInfoList(request.getBudgetDetailList());

            // 调用应用服务编辑
            String id = annualBudgetApplication.editStep1(mainEntity, budgetDetailList);

            log.info("编辑年度预算第一步成功，预算名称: {}, ID: {}", request.getBudgetName(), id);
            return Result.OK("编辑成功", id);

        } catch (Exception e) {
            log.error("编辑年度预算第一步失败，预算ID: {}, 预算名称: {}",
                    request != null ? request.getId() : "null",
                    request != null ? request.getBudgetName() : "null", e);
            return Result.error("编辑失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<BudgetSubjectInfoDTO>> queryBudgetSubjects(String projectPlanId) {
        log.info("开始查询预算科目信息，项目计划ID: {}", projectPlanId);

        try {
            // 调用应用服务查询预算科目信息
            List<AnnualBudgetApplication.BudgetSubjectInfo> subjectInfoList =
                    annualBudgetApplication.queryBudgetSubjects(projectPlanId);

            // 转换为DTO
            List<BudgetSubjectInfoDTO> resultList = new ArrayList<>();
            for (AnnualBudgetApplication.BudgetSubjectInfo info : subjectInfoList) {
                BudgetSubjectInfoDTO dto = new BudgetSubjectInfoDTO();
                BeanUtils.copyProperties(info, dto);
                resultList.add(dto);
            }

            log.info("查询预算科目信息成功，返回 {} 条记录", resultList.size());
            return Result.OK(resultList);

        } catch (Exception e) {
            log.error("查询预算科目信息失败，项目计划ID: {}", projectPlanId, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 年度预算明细导入Excel
     *
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 导入结果（CostAnnualBudgetSaveRequest格式）
     */
    @Operation(summary = "年度预算明细-导入Excel")
    @Override
    public Result<CostAnnualBudgetSaveRequest> importExcel(HttpServletRequest request, HttpServletResponse response) {
        log.info("开始导入年度预算明细Excel");

        try {
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

            if (fileMap.isEmpty()) {
                log.warn("未找到上传的Excel文件");
                return Result.error("请选择要导入的Excel文件");
            }

            // 获取上传文件对象
            MultipartFile file = fileMap.values().iterator().next();
            if (file.isEmpty()) {
                log.warn("上传的Excel文件为空");
                return Result.error("上传的Excel文件为空");
            }

            log.info("开始解析Excel文件: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());

            // 设置导入参数
            ImportParams params = new ImportParams();
            params.setTitleRows(1); // 标题行数：1行
            params.setHeadRows(1);  // 表头行数：1行（第2行是表头）
            params.setNeedSave(false); // 不需要保存到数据库

            // 使用cdkit的ExcelImportUtil解析Excel
            List<CostAnnualBudgetDetailImportDTO> importList = ExcelImportUtil.importExcel(
                    file.getInputStream(),
                    CostAnnualBudgetDetailImportDTO.class,
                    params
            );

            if (importList == null || importList.isEmpty()) {
                log.warn("Excel文件中没有有效数据");
                return Result.error("Excel文件中没有有效数据");
            }

            log.info("成功解析Excel文件，共 {} 条数据", importList.size());

            // 转换为CostAnnualBudgetSaveRequest格式
            CostAnnualBudgetSaveRequest saveRequest = importConverter.toSaveRequest(importList);

            if (saveRequest == null) {
                log.error("数据转换失败");
                return Result.error("数据转换失败");
            }

            log.info("年度预算明细导入成功，共 {} 条明细数据",
                    saveRequest.getBudgetDetailList() != null ? saveRequest.getBudgetDetailList().size() : 0);

            return Result.OK("导入成功", saveRequest);

        } catch (Exception e) {
            log.error("年度预算明细导入失败", e);
            return Result.error("导入失败：" + e.getMessage());
        }
    }

    /**
     * Step2 导入中心间接成本Excel
     *
     * @param annualBudgetId 年度总预算的ID
     * @param templateType 模版类型
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 导入结果
     */
    @Operation(summary = "Step2 导入中心间接成本Excel")
    @Override
    public Result<String> importStep2Excel(String annualBudgetId, String templateType,
                                          HttpServletRequest request, HttpServletResponse response) {
        log.info("开始Step2导入中心间接成本Excel，年度预算ID: {}, 模版类型: {}", annualBudgetId, templateType);

        try {
            // 参数验证
            if (!StringUtils.hasText(annualBudgetId)) {
                return Result.error("年度总预算ID不能为空");
            }
            if (!StringUtils.hasText(templateType)) {
                return Result.error("模版类型不能为空");
            }

            // 验证模版类型
            if (!isValidTemplateType(templateType)) {
                return Result.error("无效的模版类型：" + templateType);
            }

            // 验证年度预算是否存在
            CostAnnualBudgetEntity budgetEntity = annualBudgetApplication.queryById(annualBudgetId);
            if (budgetEntity == null) {
                return Result.error("年度总预算不存在，ID: " + annualBudgetId);
            }

            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

            if (fileMap.isEmpty()) {
                log.warn("未找到上传的Excel文件");
                return Result.error("请选择要导入的Excel文件");
            }

            // 获取上传文件对象
            MultipartFile file = fileMap.values().iterator().next();
            if (file.isEmpty()) {
                log.warn("上传的Excel文件为空");
                return Result.error("上传的Excel文件为空");
            }

            log.info("开始解析Excel文件: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());

            // 使用Hutool读取多个sheet页
            List<CenterCostImportDTO> importDataList = parseExcelWithMultipleSheets(
                    file.getInputStream(), budgetEntity.getBudgetCode(), templateType);

            if (importDataList.isEmpty()) {
                log.warn("Excel文件中没有有效数据");
                return Result.error("Excel文件中没有有效数据");
            }

            // 校验预算科目是否存在，并设置科目编码
            String validationResult = validateAndSetSubjectCodes(importDataList);
            if (validationResult != null) {
                log.warn("预算科目校验失败: {}", validationResult);
                return Result.error(validationResult);
            }

            // 通过Application层保存数据，避免直接依赖Infrastructure层
            int savedCount = annualBudgetApplication.saveCenterCostImportData(importDataList);

            log.info("Step2导入中心间接成本Excel成功，共导入 {} 条数据", savedCount);
            return Result.OK("导入成功，共导入 " + savedCount + " 条数据");

        } catch (Exception e) {
            log.error("Step2导入中心间接成本Excel失败", e);
            return Result.error("导入失败：" + e.getMessage());
        }
    }

    /**
     * 验证模版类型是否有效
     */
    private boolean isValidTemplateType(String templateType) {
        return "this_center_indirect_cost_template".equals(templateType) ||
               "non_operational_center_indirect_cost_template".equals(templateType) ||
               "general_admin_indirect_cost_template".equals(templateType);
    }

    /**
     * 使用POI直接解析包含多个sheet页的Excel文件
     */
    private List<CenterCostImportDTO> parseExcelWithMultipleSheets(
            InputStream inputStream, String budgetCode, String templateType) throws Exception {

        List<CenterCostImportDTO> resultList = new ArrayList<>();

        try (Workbook workbook = WorkbookFactory.create(inputStream)) {
            int numberOfSheets = workbook.getNumberOfSheets();
            log.info("Excel文件包含 {} 个sheet页", numberOfSheets);

            for (int sheetIndex = 0; sheetIndex < numberOfSheets; sheetIndex++) {
                String sheetName = workbook.getSheetName(sheetIndex);
                log.info("开始处理第 {} 个sheet页: {}", sheetIndex + 1, sheetName);

                try {
                    // 直接使用POI处理sheet页
                    org.apache.poi.ss.usermodel.Sheet sheet = workbook.getSheetAt(sheetIndex);
                    List<Map<String, Object>> rows = parseSheetData(sheet);

                    log.info("从sheet页 {} 中读取到 {} 行数据", sheetName, rows.size());

                    // 使用转换器将行数据转换为DTO
                    List<CenterCostImportDTO> sheetDataList = CenterCostImportConverter.convertRowsToDTO(
                            rows, budgetCode, sheetName, templateType);
                    resultList.addAll(sheetDataList);
                } catch (Exception e) {
                    log.error("处理sheet页 {} 失败: {}", sheetName, e.getMessage());
                    // 继续处理下一个sheet页
                }
            }
        }

        log.info("成功解析Excel文件，共解析到 {} 条数据", resultList.size());
        return resultList;
    }

    /**
     * 使用POI直接解析sheet页数据
     */
    private List<Map<String, Object>> parseSheetData(org.apache.poi.ss.usermodel.Sheet sheet) {
        List<Map<String, Object>> resultList = new ArrayList<>();

        if (sheet == null) {
            return resultList;
        }

        // 获取表头行（假设第一行是表头）
        org.apache.poi.ss.usermodel.Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            return resultList;
        }

        // 解析表头，建立列索引映射
        Map<Integer, String> columnMapping = new HashMap<>();
        for (int cellIndex = 0; cellIndex < headerRow.getLastCellNum(); cellIndex++) {
            org.apache.poi.ss.usermodel.Cell cell = headerRow.getCell(cellIndex);
            if (cell != null) {
                String headerValue = getCellStringValue(cell);
                String fieldName = mapHeaderToField(headerValue);
                log.debug("表头映射: 列{} -> '{}' -> '{}'", cellIndex, headerValue, fieldName);
                if (fieldName != null) {
                    columnMapping.put(cellIndex, fieldName);
                }
            }
        }
        log.info("表头映射完成，共映射 {} 列", columnMapping.size());

        // 解析数据行（从第二行开始）
        for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            org.apache.poi.ss.usermodel.Row dataRow = sheet.getRow(rowIndex);
            if (dataRow == null) {
                continue;
            }

            Map<String, Object> rowData = new HashMap<>();
            boolean hasData = false;

            for (Map.Entry<Integer, String> entry : columnMapping.entrySet()) {
                int cellIndex = entry.getKey();
                String fieldName = entry.getValue();

                org.apache.poi.ss.usermodel.Cell cell = dataRow.getCell(cellIndex);
                Object cellValue = getCellValue(cell);

                if (cellValue != null) {
                    rowData.put(fieldName, cellValue);
                    hasData = true;
                    // 特别记录金额字段的值
                    if ("costAmount".equals(fieldName)) {
                        log.debug("第{}行，金额字段值: {} (类型: {})", rowIndex + 1, cellValue, cellValue.getClass().getSimpleName());
                    }
                }
            }

            if (hasData) {
                resultList.add(rowData);
            }
        }

        return resultList;
    }

    /**
     * 将表头映射到字段名
     */
    private String mapHeaderToField(String headerValue) {
        if (headerValue == null || headerValue.trim().isEmpty()) {
            return null;
        }

        String header = headerValue.trim();

        // 根据Excel截图，设置表头映射
        if ("间接费预算科目".equals(header) || "预算科目".equals(header) ||
            "预算科目名称".equals(header) || "科目名称".equals(header)) {
            return "subjectName";
        } else if ("科目预算金额（元）".equals(header) || "科目预算金额(元)".equals(header) ||
                   "科目预算金额 (元)".equals(header) || "科目预算金额(万元)".equals(header) ||
                   "金额".equals(header) || "预算金额".equals(header) || "成本金额".equals(header) ||
                   "成本金额(万元)".equals(header)) {
            return "costAmount";
        } else if ("备注".equals(header)) {
            return "remark";
        }

        return null;
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(org.apache.poi.ss.usermodel.Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }

    /**
     * 获取单元格值
     */
    private Object getCellValue(org.apache.poi.ss.usermodel.Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else {
                    return cell.getNumericCellValue();
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }

    /**
     * 设置表头别名，将Excel表头映射到实体字段（保留原方法以兼容）
     */
    private void setupHeaderAlias(ExcelReader reader) {
        // 根据Excel截图，设置表头别名
        reader.addHeaderAlias("间接费预算科目", "subjectName");
        reader.addHeaderAlias("科目预算金额(万元)", "costAmount");
        reader.addHeaderAlias("备注", "remark");

        // 可能的其他表头格式
        reader.addHeaderAlias("预算科目", "subjectName");
        reader.addHeaderAlias("预算科目名称", "subjectName");
        reader.addHeaderAlias("科目名称", "subjectName");
        reader.addHeaderAlias("金额", "costAmount");
        reader.addHeaderAlias("预算金额", "costAmount");
        reader.addHeaderAlias("成本金额", "costAmount");
        reader.addHeaderAlias("成本金额(万元)", "costAmount");
    }



    /**
     * 校验预算科目是否存在，并设置科目编码
     *
     * @param importDataList 导入数据列表
     * @return 校验失败信息，null表示校验通过
     */
    private String validateAndSetSubjectCodes(List<CenterCostImportDTO> importDataList) {
        try {
            // 获取所有启用状态的预算科目
            List<CostBudgetSubjectEntity> enabledSubjects = costBudgetSubjectRepository.findAllEnabled();

            // 构建科目名称到科目编码的映射
            Map<String, String> subjectNameToCodeMap = enabledSubjects.stream()
                    .collect(Collectors.toMap(
                            CostBudgetSubjectEntity::getSubjectName,
                            CostBudgetSubjectEntity::getSubjectCode,
                            (existing, replacement) -> existing // 如果有重复，保留第一个
                    ));

            log.info("系统中共有 {} 个启用状态的预算科目", subjectNameToCodeMap.size());

            // 收集导入数据中的所有科目名称
            Set<String> importSubjectNames = importDataList.stream()
                    .map(CenterCostImportDTO::getSubjectName)
                    .filter(name -> name != null && !name.trim().isEmpty())
                    .collect(Collectors.toSet());

            log.info("导入数据中共有 {} 个不同的科目名称", importSubjectNames.size());

            // 找出不存在的科目
            Set<String> invalidSubjects = new HashSet<>();
            for (String importSubjectName : importSubjectNames) {
                if (!subjectNameToCodeMap.containsKey(importSubjectName)) {
                    invalidSubjects.add(importSubjectName);
                }
            }

            if (!invalidSubjects.isEmpty()) {
                String invalidSubjectList = String.join("、", invalidSubjects);
                String errorMessage = String.format("导入失败：以下间接费预算科目不在系统预算科目中，请检查：%s", invalidSubjectList);
                log.warn("预算科目校验失败，无效科目: {}", invalidSubjectList);
                return errorMessage;
            }

            // 校验通过，为所有导入数据设置科目编码
            for (CenterCostImportDTO importData : importDataList) {
                String subjectName = importData.getSubjectName();
                if (subjectName != null && !subjectName.trim().isEmpty()) {
                    String subjectCode = subjectNameToCodeMap.get(subjectName);
                    importData.setSubjectCode(subjectCode);
                    log.debug("为科目 {} 设置编码: {}", subjectName, subjectCode);
                }
            }

            log.info("预算科目校验通过，所有科目都存在于系统中，已设置科目编码");
            return null; // 校验通过

        } catch (Exception e) {
            log.error("预算科目校验过程中发生异常", e);
            return "预算科目校验失败：" + e.getMessage();
        }
    }

}
